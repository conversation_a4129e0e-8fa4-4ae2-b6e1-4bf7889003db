using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Raylib-based scene to replace DirectX scene
    /// </summary>
    public class RaylibScene : IDisposable
    {
        private RaylibRenderer renderer;
        private RaylibMIDIRenderer midiRenderer;
        private Settings settings;
        private bool isInitialized = false;
        private int windowWidth = 800;
        private int windowHeight = 600;

        public Settings Settings 
        { 
            get => settings; 
            set => settings = value; 
        }

        public MIDIFile File
        {
            get => midiRenderer?.File;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.File = value;
            }
        }

        public PlayingState Time
        {
            get => midiRenderer?.Time;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.Time = value;
            }
        }

        public long LastRenderedNoteCount => midiRenderer?.LastRenderedNoteCount ?? 0;
        public long LastNPS => midiRenderer?.LastNPS ?? 0;
        public long LastPolyphony => midiRenderer?.LastPolyphony ?? 0;
        public long NotesPassedSum => midiRenderer?.NotesPassedSum ?? 0;

        // Public access to renderer for settings updates
        public RaylibRenderer Renderer => renderer;

        /// <summary>
        /// Trigger buffer reset for future notes (called when seeking or changing speed)
        /// </summary>
        public void TriggerBufferReset()
        {
            midiRenderer?.TriggerBufferReset();
        }

        public RaylibScene(Settings settings)
        {
            this.settings = settings;
        }

        public void Initialize(int width, int height, string title)
        {
            if (isInitialized)
                return;

            windowWidth = width;
            windowHeight = height;

            Console.WriteLine("Initializing raylib window...");
            Raylib.SetConfigFlags(ConfigFlags.FLAG_WINDOW_RESIZABLE);
            Raylib.InitWindow(width, height, title);
            Raylib.SetTargetFPS(settings.General.FPSLock);

            Console.WriteLine("Creating renderer...");
            renderer = new RaylibRenderer(settings);
            renderer.SetScreenSize(width, height);

            Console.WriteLine("Creating MIDI renderer...");
            midiRenderer = new RaylibMIDIRenderer(renderer, settings);

            isInitialized = true;
            Console.WriteLine("Raylib initialization complete.");
        }

        public void RenderMIDIContent()
        {
            if (!isInitialized)
                return;

            // Handle window resize
            int currentWidth = Raylib.GetScreenWidth();
            int currentHeight = Raylib.GetScreenHeight();
            if (currentWidth != windowWidth || currentHeight != windowHeight)
            {
                windowWidth = currentWidth;
                windowHeight = currentHeight;
                renderer?.SetScreenSize(windowWidth, windowHeight);
            }

            try
            {
                // Render MIDI content only (no BeginDrawing/EndDrawing here)
                if (renderer != null && midiRenderer != null)
                {
                    renderer.BeginFrame();
                    midiRenderer.Render();
                    renderer.EndFrame();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MIDI render error: {ex.Message}");
            }
        }

        public bool ShouldClose()
        {
            return !isInitialized || Raylib.WindowShouldClose();
        }

        public void SetScreenSize(int width, int height)
        {
            windowWidth = width;
            windowHeight = height;
            renderer?.SetScreenSize(width, height);
        }

        public void Dispose()
        {
            if (!isInitialized)
                return;

            midiRenderer?.Dispose();
            renderer?.Dispose();
            
            if (Raylib.IsWindowReady())
            {
                Raylib.CloseWindow();
            }

            isInitialized = false;
        }
    }

    /// <summary>
    /// MIDI-specific renderer using raylib
    /// </summary>
    public class RaylibMIDIRenderer : IDisposable
    {
        private RaylibRenderer renderer;
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private object fileLock = new object();

        // Statistics
        public long LastRenderedNoteCount { get; private set; } = 0;
        public long LastNPS { get; private set; } = 0;
        public long LastPolyphony { get; private set; } = 0;
        public long NotesPassedSum { get; private set; } = 0;

        // Future Notes Buffer System (from original Kiva MIDI)
        private struct FutureNote
        {
            public int keyIndex;
            public double start;
            public double end;
            public int colorPointer;
        }

        private List<FutureNote> futureNotesBuffer = new List<FutureNote>();
        private double lastBufferTime = -1;
        private double lastBufferTimeScale = -1;
        private bool bufferNeedsReset = true;

        // Keyboard layout (public for sliding texture access)
        public bool[] blackKeys = new bool[257];
        public double[] x1array = new double[257];
        public double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        public MIDIFile File
        {
            get => file;
            set
            {
                lock (fileLock)
                {
                    file = value;
                }
            }
        }

        public PlayingState Time
        {
            get => time;
            set => time = value;
        }

        public RaylibMIDIRenderer(RaylibRenderer renderer, Settings settings)
        {
            this.renderer = renderer;
            this.settings = settings;
            InitializeKeyboardLayout();
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize black key pattern
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            // Calculate keyboard layout
            CalculateKeyPositions();
        }

        private void CalculateKeyPositions()
        {
            int firstNote = 0;
            int lastNote = 128;
            
            // Apply key range settings
            if (settings.General.KeyRange == KeyRangeTypes.Key88)
            {
                firstNote = 21;
                lastNote = 109;
            }
            else if (settings.General.KeyRange == KeyRangeTypes.Key256)
            {
                firstNote = 0;
                lastNote = 256;
            }

            // Calculate white key positions
            int whiteKeyCount = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i]) whiteKeyCount++;
            }

            double whiteKeyWidth = 1.0 / whiteKeyCount;
            int whiteKeyIndex = 0;

            fullLeft = 0;
            fullRight = 1;
            fullWidth = 1;

            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i])
                {
                    // White key
                    x1array[i] = whiteKeyIndex * whiteKeyWidth;
                    wdtharray[i] = whiteKeyWidth;
                    whiteKeyIndex++;
                }
                else
                {
                    // Black key
                    double blackKeyWidth = whiteKeyWidth * 0.6;
                    x1array[i] = (whiteKeyIndex - 0.5) * whiteKeyWidth - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
            }
        }

        public void Render()
        {
            if (time == null || renderer == null)
                return;

            double currentTime = time.GetTime();
            double timeScale = settings?.Volatile?.Size ?? 1.0;
            double renderCutoff = currentTime + timeScale;

            // Check if buffer needs reset (seeking or speed change)
            if (bufferNeedsReset || Math.Abs(currentTime - lastBufferTime) > 0.1 ||
                Math.Abs(timeScale - lastBufferTimeScale) > 0.001)
            {
                ResetFutureNotesBuffer();
                bufferNeedsReset = false;
            }

            lock (fileLock)
            {
                if (file != null && file is MIDIMemoryFile memoryFile)
                {
                    try
                    {
                        // Update future notes buffer if needed
                        UpdateFutureNotesBuffer(memoryFile, currentTime, timeScale, renderCutoff);

                        // Render from buffer
                        RenderFromFutureNotesBuffer(memoryFile, currentTime, timeScale);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error rendering MIDI file: {ex.Message}");
                    }
                }
            }

            // Render using sliding texture system instead of direct note rendering
            try
            {
                // Render sliding texture with future notes
                renderer.RenderSlidingTexture(currentTime, timeScale);

                // Render keyboard on top
                renderer.RenderKeyboard();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering sliding texture/keyboard: {ex.Message}");
            }
        }

        private void RenderMIDIFile(MIDIMemoryFile file, double currentTime, double timeScale, double renderCutoff)
        {
            if (file == null || renderer == null)
                return;

            try
            {
                file.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            var colors = file.MidiNoteColors;
            if (colors == null)
            {
                Console.WriteLine("Warning: MidiNoteColors is null");
                return;
            }

            long notesRendered = 0;
            int polyphony = 0;

            // Clear all key states first
            for (int k = 0; k < 256; k++)
            {
                renderer.UpdateKey(k, new RaylibColor(0, 0, 0, 0), new RaylibColor(0, 0, 0, 0), false, 0);
            }

            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (blackKeys[k]) continue; // Skip black keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            // Render black key notes second (foreground layer) - they will overlap white key notes
            for (int k = 0; k < 256; k++)
            {
                if (!blackKeys[k]) continue; // Skip white keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            LastRenderedNoteCount = notesRendered;
            LastPolyphony = polyphony;

            // Update last render time for optimization tracking
            file.lastRenderTime = currentTime;
        }

        private void RenderNotesForKey(MIDIMemoryFile file, int k, double currentTime, double timeScale, double renderCutoff, NoteCol[] colors, ref long notesRendered, ref int polyphony)
        {
            if (file.Notes == null || k >= file.Notes.Length)
                return;

            var notes = file.Notes[k];
            if (notes == null || notes.Length == 0)
                return;

            // Use original piano layout positioning
            float left = (float)((x1array[k] - fullLeft) / fullWidth);
            float right = (float)((x1array[k] + wdtharray[k] - fullLeft) / fullWidth);
            bool pressed = false;
            var keyColor = new RaylibColor(0, 0, 0, 0);

            // PROPER OPTIMIZATION: Use FirstRenderNote but update it more carefully
            int startIndex = file.FirstRenderNote[k];

            // Handle seeking (time going backwards)
            if (currentTime < file.lastRenderTime)
            {
                // Reset to find the first note that might still be visible
                startIndex = 0;
                for (int i = 0; i < notes.Length; i++)
                {
                    if (notes[i].end > currentTime - 1.0) // 1 second buffer for seeking
                    {
                        startIndex = i;
                        break;
                    }
                }
                file.FirstRenderNote[k] = startIndex;
            }

            // Advance FirstRenderNote ONLY for notes that are completely done
            // This happens BEFORE processing to ensure we don't skip visible notes
            while (startIndex < notes.Length && notes[startIndex].end < currentTime - 1.0)
            {
                startIndex++;
            }
            file.FirstRenderNote[k] = startIndex;

            // Process notes starting from the optimized index
            for (int i = startIndex; i < notes.Length; i++)
            {
                var note = notes[i];

                // If note starts too far in the future, we're done
                if (note.start > renderCutoff)
                    break;

                // Check if key is currently pressed
                if (note.start <= currentTime && note.end >= currentTime)
                {
                    pressed = true;
                    polyphony++;

                    // Get note color
                    var noteCol = colors[note.colorPointer];
                    keyColor = ColorFromNoteCol(noteCol);
                }

                // Add note to render buffer if it's visible
                if (note.start <= renderCutoff && note.end >= currentTime)
                {
                    float noteStart = (float)((note.start - currentTime) / timeScale);
                    float noteEnd = (float)((note.end - currentTime) / timeScale);
                    var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                    // Use AddNoteByKey to get proper piano key widths
                    renderer.AddNoteByKey(k, noteStart, noteEnd, noteColor, noteColor);
                    notesRendered++;
                }
            }

            // Update key state
            renderer.UpdateKey(k, keyColor, keyColor, pressed, 0);
        }

        /// <summary>
        /// Reset the future notes buffer - called when seeking or changing speed
        /// </summary>
        private void ResetFutureNotesBuffer()
        {
            futureNotesBuffer.Clear();
            lastBufferTime = -1;
            lastBufferTimeScale = -1;
            Console.WriteLine("Future notes buffer reset");
        }

        /// <summary>
        /// Update the future notes buffer with notes that will be visible
        /// </summary>
        private void UpdateFutureNotesBuffer(MIDIMemoryFile file, double currentTime, double timeScale, double renderCutoff)
        {
            // Only rebuild buffer if time parameters changed significantly
            if (Math.Abs(currentTime - lastBufferTime) < 0.01 &&
                Math.Abs(timeScale - lastBufferTimeScale) < 0.001)
            {
                return; // Buffer is still valid
            }

            futureNotesBuffer.Clear();

            // Use the same FirstRenderNote optimization as original Kiva
            for (int k = 0; k < 256; k++)
            {
                if (file.Notes == null || k >= file.Notes.Length)
                    continue;

                var notes = file.Notes[k];
                if (notes == null || notes.Length == 0)
                    continue;

                // Start from FirstRenderNote index for optimization
                int startIndex = file.FirstRenderNote[k];

                // Handle seeking backwards (like original Kiva)
                if (currentTime < file.lastRenderTime)
                {
                    startIndex = 0;
                    for (int i = 0; i < notes.Length; i++)
                    {
                        if (notes[i].end > currentTime)
                        {
                            startIndex = i;
                            break;
                        }
                    }
                    file.FirstRenderNote[k] = startIndex;
                }

                // Advance FirstRenderNote for notes that are completely done
                while (startIndex < notes.Length && notes[startIndex].end < currentTime)
                {
                    startIndex++;
                }
                file.FirstRenderNote[k] = startIndex;

                // Add future notes to buffer (notes that start before renderCutoff)
                for (int i = startIndex; i < notes.Length; i++)
                {
                    var note = notes[i];

                    // Stop if note starts too far in the future
                    if (note.start > renderCutoff)
                        break;

                    // Add note to buffer if it will be visible
                    if (note.end >= currentTime)
                    {
                        futureNotesBuffer.Add(new FutureNote
                        {
                            keyIndex = k,
                            start = note.start,
                            end = note.end,
                            colorPointer = note.colorPointer
                        });
                    }
                }
            }

            lastBufferTime = currentTime;
            lastBufferTimeScale = timeScale;

            Console.WriteLine($"Future notes buffer updated: {futureNotesBuffer.Count} notes buffered");
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        /// <summary>
        /// Render notes from the future notes buffer using sliding texture
        /// </summary>
        private void RenderFromFutureNotesBuffer(MIDIMemoryFile file, double currentTime, double timeScale)
        {
            if (file == null || renderer == null)
                return;

            try
            {
                file.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
            }

            var colors = file.MidiNoteColors;
            if (colors == null)
            {
                Console.WriteLine("Warning: MidiNoteColors is null");
                return;
            }

            long notesRendered = 0;
            int polyphony = 0;

            // Clear all key states first
            for (int k = 0; k < 256; k++)
            {
                renderer.UpdateKey(k, new RaylibColor(0, 0, 0, 0), new RaylibColor(0, 0, 0, 0), false, 0);
            }

            // Track pressed keys for polyphony calculation
            bool[] keyPressed = new bool[256];

            // Convert future notes buffer to RaylibRenderNote format for sliding texture
            var renderNotes = new List<RaylibRenderNote>();

            // Process notes from buffer
            foreach (var futureNote in futureNotesBuffer)
            {
                // Check if note is currently active (for key press visualization)
                if (futureNote.start <= currentTime && futureNote.end >= currentTime)
                {
                    if (!keyPressed[futureNote.keyIndex])
                    {
                        keyPressed[futureNote.keyIndex] = true;
                        polyphony++;

                        // Update key visual state
                        var noteCol = colors[futureNote.colorPointer];
                        var keyColor = ColorFromNoteCol(noteCol);
                        renderer.UpdateKey(futureNote.keyIndex, keyColor, keyColor, true, 1.0f);
                    }
                }

                // Add note to sliding texture render list if it will be visible
                if (futureNote.start <= currentTime + timeScale && futureNote.end >= currentTime)
                {
                    float noteStart = (float)((futureNote.start - currentTime) / timeScale);
                    float noteEnd = (float)((futureNote.end - currentTime) / timeScale);
                    var noteColor = ColorFromNoteCol(colors[futureNote.colorPointer]);

                    // Calculate note position using keyboard layout from renderer
                    float noteLeft = (float)renderer.x1array[futureNote.keyIndex];
                    float noteRight = noteLeft + (float)renderer.wdtharray[futureNote.keyIndex];

                    // Normalize to 0-1 range for texture coordinates
                    float fullLeft = (float)renderer.x1array.Min();
                    float fullWidth = (float)(renderer.x1array.Max() + renderer.wdtharray.Max()) - fullLeft;
                    noteLeft = (noteLeft - fullLeft) / fullWidth;
                    noteRight = (noteRight - fullLeft) / fullWidth;

                    renderNotes.Add(new RaylibRenderNote
                    {
                        left = noteLeft,
                        right = noteRight,
                        start = noteStart,
                        end = noteEnd,
                        colorLeft = noteColor,
                        colorRight = noteColor
                    });

                    notesRendered++;
                }
            }

            // Update sliding texture with the render notes
            renderer.UpdateSlidingTexture(renderNotes, currentTime, timeScale);

            LastRenderedNoteCount = notesRendered;
            LastPolyphony = polyphony;

            // Update last render time
            file.lastRenderTime = currentTime;
        }

        /// <summary>
        /// Trigger buffer reset (called from external sources like seeking)
        /// </summary>
        public void TriggerBufferReset()
        {
            bufferNeedsReset = true;
            renderer?.MarkSlidingTextureForUpdate();
        }

        private bool IsBlackNote(int noteNumber)
        {
            int n = noteNumber % 12;
            return n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
