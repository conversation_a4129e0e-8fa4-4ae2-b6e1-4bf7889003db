using System;
using System.Collections.Generic;
using System.Numerics;
using System.Runtime.InteropServices;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;
using Texture2D = Kiva_MIDI.RaylibPInvoke.Texture2D;
using RenderTexture2D = Kiva_MIDI.RaylibPInvoke.RenderTexture2D;

namespace Kiva_MIDI
{
    /// <summary>
    /// Dual-player system for raylib renderer implementing the original Kiva MIDI future notes buffer approach.
    /// MainPlayer handles normal playback, SecondPlayer pre-renders future notes to a sliding texture.
    /// </summary>
    public class RaylibDualPlayer : IDisposable
    {
        private Settings settings;
        private RaylibRenderer renderer;
        private MIDIFile file;
        private PlayingState time;
        
        // Dual player components
        private MainPlayer mainPlayer;
        private SecondPlayer secondPlayer;
        
        // Texture management
        private RenderTexture2D notesTexture;
        private int textureWidth = 2048;
        private int textureHeight = 4096; // Large texture for high quality
        private bool textureNeedsReset = false;
        
        // Buffer management (similar to original Kiva)
        private double lastTime = 0;
        private double lastSpeed = 1.0;
        private bool needsBufferReset = false;
        
        // Statistics
        public long LastRenderedNoteCount { get; private set; } = 0;
        public long LastNPS { get; private set; } = 0;
        public long LastPolyphony { get; private set; } = 0;
        public long NotesPassedSum { get; private set; } = 0;
        
        public MIDIFile File
        {
            get => file;
            set
            {
                file = value;
                if (mainPlayer != null) mainPlayer.File = value;
                if (secondPlayer != null) secondPlayer.File = value;
                RequestBufferReset();
            }
        }
        
        public PlayingState Time
        {
            get => time;
            set
            {
                time = value;
                if (mainPlayer != null) mainPlayer.Time = value;
                if (secondPlayer != null) secondPlayer.Time = value;
            }
        }
        
        public RaylibDualPlayer(RaylibRenderer renderer, Settings settings)
        {
            this.renderer = renderer;
            this.settings = settings;
            
            InitializeTexture();
            InitializePlayers();
        }
        
        private void InitializeTexture()
        {
            try
            {
                // Create large render texture for pre-rendered notes
                notesTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
                Console.WriteLine($"Created notes texture: {textureWidth}x{textureHeight}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating notes texture: {ex.Message}");
            }
        }
        
        private void InitializePlayers()
        {
            mainPlayer = new MainPlayer(renderer, settings);
            secondPlayer = new SecondPlayer(renderer, settings, notesTexture);
            
            if (file != null)
            {
                mainPlayer.File = file;
                secondPlayer.File = file;
            }
            
            if (time != null)
            {
                mainPlayer.Time = time;
                secondPlayer.Time = time;
            }
        }
        
        public void Render()
        {
            if (time == null || file == null)
                return;
                
            double currentTime = time.GetTime();
            double currentSpeed = time.Speed;
            
            // Check if we need to reset the buffer (seeking or speed change)
            CheckForBufferReset(currentTime, currentSpeed);
            
            // Update players
            mainPlayer.Update(currentTime);
            secondPlayer.Update(currentTime, needsBufferReset);
            
            // Render using the sliding texture approach
            RenderWithSlidingTexture(currentTime);
            
            // Update statistics
            UpdateStatistics();
            
            // Reset flags
            needsBufferReset = false;
            lastTime = currentTime;
            lastSpeed = currentSpeed;
        }
        
        private void CheckForBufferReset(double currentTime, double currentSpeed)
        {
            // Reset buffer if user seeks (time jumps) or changes speed
            bool timeJumped = Math.Abs(currentTime - lastTime) > 0.1; // 100ms threshold
            bool speedChanged = Math.Abs(currentSpeed - lastSpeed) > 0.01;
            
            if (timeJumped || speedChanged || textureNeedsReset)
            {
                needsBufferReset = true;
                textureNeedsReset = false;
                Console.WriteLine($"Buffer reset: timeJump={timeJumped}, speedChange={speedChanged}");
            }
        }
        
        private void RenderWithSlidingTexture(double currentTime)
        {
            // Render the pre-rendered texture with sliding offset
            if (notesTexture.id > 0 && secondPlayer != null)
            {
                // Calculate texture offset based on time progression
                float timeScale = (float)(settings?.Volatile?.Size ?? 1.0);
                double timeDelta = currentTime - secondPlayer.BufferStartTime;

                // Convert time delta to texture pixel offset
                // The texture represents bufferDuration seconds of time
                float textureOffset = (float)(timeDelta / secondPlayer.BufferDuration * textureHeight);

                // Ensure we don't go beyond texture bounds
                textureOffset = Math.Max(0, Math.Min(textureHeight - renderer.ScreenHeight, textureOffset));

                // Create source rectangle (what part of texture to draw)
                Rectangle sourceRect = new Rectangle(
                    0,
                    textureOffset,
                    Math.Min(textureWidth, renderer.ScreenWidth),
                    Math.Min(textureHeight - textureOffset, renderer.ScreenHeight)
                );

                // Create destination rectangle (where to draw on screen)
                Rectangle destRect = new Rectangle(
                    0,
                    0,
                    renderer.ScreenWidth,
                    renderer.ScreenHeight
                );

                try
                {
                    // Draw the sliding texture with proper scaling
                    Raylib.DrawTexturePro(notesTexture.texture, sourceRect, destRect,
                                        new Vector2(0, 0), 0, RaylibColor.WHITE);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error drawing sliding texture: {ex.Message}");
                }
            }

            // Render current keyboard state from main player
            mainPlayer.RenderKeyboard();
        }
        
        private void UpdateStatistics()
        {
            LastRenderedNoteCount = mainPlayer.LastRenderedNoteCount + secondPlayer.LastRenderedNoteCount;
            LastNPS = mainPlayer.LastNPS;
            LastPolyphony = mainPlayer.LastPolyphony;
            NotesPassedSum = mainPlayer.NotesPassedSum;
        }
        
        public void RequestBufferReset()
        {
            textureNeedsReset = true;
            Console.WriteLine("Buffer reset requested");
        }

        /// <summary>
        /// Get debug information about the dual-player system
        /// </summary>
        public string GetDebugInfo()
        {
            if (secondPlayer == null) return "SecondPlayer not initialized";

            return $"Buffer: {bufferStartTime:F2}s - {secondPlayer.BufferStartTime + secondPlayer.BufferDuration:F2}s " +
                   $"({secondPlayer.BufferDuration:F2}s duration), " +
                   $"Texture: {textureWidth}x{textureHeight}, " +
                   $"Notes: {LastRenderedNoteCount:N0}";
        }
        
        public void Dispose()
        {
            mainPlayer?.Dispose();
            secondPlayer?.Dispose();

            if (notesTexture.id > 0)
            {
                Raylib.UnloadRenderTexture(notesTexture);
            }
        }
    }

    /// <summary>
    /// MainPlayer handles normal playback and keyboard state updates
    /// </summary>
    public class MainPlayer : IDisposable
    {
        private RaylibRenderer renderer;
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;

        // Keyboard layout (copied from original)
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        // Statistics
        public long LastRenderedNoteCount { get; private set; } = 0;
        public long LastNPS { get; private set; } = 0;
        public long LastPolyphony { get; private set; } = 0;
        public long NotesPassedSum { get; private set; } = 0;

        public MIDIFile File
        {
            get => file;
            set => file = value;
        }

        public PlayingState Time
        {
            get => time;
            set => time = value;
        }

        public MainPlayer(RaylibRenderer renderer, Settings settings)
        {
            this.renderer = renderer;
            this.settings = settings;
            InitializeKeyboardLayout();
        }

        private void InitializeKeyboardLayout()
        {
            // Copy keyboard layout initialization from RaylibMIDIRenderer
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            CalculateKeyboardLayout();
        }

        private bool IsBlackNote(int note)
        {
            int octaveNote = note % 12;
            return octaveNote == 1 || octaveNote == 3 || octaveNote == 6 || octaveNote == 8 || octaveNote == 10;
        }

        private void CalculateKeyboardLayout()
        {
            // Use traditional piano layout with alternating big-small key widths
            int firstNote = 0;
            int lastNote = 128;

            // Apply key range settings
            switch (settings.General.KeyRange)
            {
                case KeyRangeTypes.Key88:
                    firstNote = 21;
                    lastNote = 109;
                    break;
                case KeyRangeTypes.Key128:
                    firstNote = 0;
                    lastNote = 128;
                    break;
                case KeyRangeTypes.Key256:
                    firstNote = 0;
                    lastNote = 256;
                    break;
                case KeyRangeTypes.KeyMIDI:
                    firstNote = settings.General.FirstKey;
                    lastNote = settings.General.LastKey + 1;
                    break;
                case KeyRangeTypes.KeyDynamic:
                    firstNote = settings.General.FirstKey;
                    lastNote = settings.General.LastKey + 1;
                    break;
                case KeyRangeTypes.Custom:
                    firstNote = settings.General.CustomFirstKey;
                    lastNote = settings.General.CustomLastKey + 1;
                    break;
            }

            // Calculate key positions using traditional piano layout
            double currentX = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (blackKeys[i])
                {
                    // Black keys are narrower
                    wdtharray[i] = 0.6; // Smaller width for black keys
                }
                else
                {
                    // White keys are wider
                    wdtharray[i] = 1.0; // Standard width for white keys
                }

                x1array[i] = currentX;
                currentX += wdtharray[i];
            }

            fullLeft = x1array[firstNote];
            fullRight = currentX;
            fullWidth = fullRight - fullLeft;
        }

        public void Update(double currentTime)
        {
            if (file == null || time == null)
                return;

            // Main player only handles keyboard state updates
            // Notes are rendered by the SecondPlayer to texture
            UpdateKeyboardState(currentTime);
        }

        private void UpdateKeyboardState(double currentTime)
        {
            if (!(file is MIDIMemoryFile memoryFile))
                return;

            int polyphony = 0;

            // Update keyboard state for all keys
            for (int k = 0; k < 256; k++)
            {
                var notes = memoryFile.Notes[k];
                if (notes == null || notes.Length == 0)
                    continue;

                bool pressed = false;
                var keyColor = new RaylibColor(0, 0, 0, 0);

                // Check if any note is currently playing on this key
                foreach (var note in notes)
                {
                    if (note.start <= currentTime && note.end >= currentTime)
                    {
                        pressed = true;
                        polyphony++;

                        // Get note color
                        var noteColor = memoryFile.MidiNoteColors[note.colorPointer];
                        keyColor = ColorFromNoteCol(noteColor);
                        break; // Only need one active note per key
                    }
                }

                // Update key state in renderer
                renderer.UpdateKey(k, keyColor, keyColor, pressed, 0);
            }

            LastPolyphony = polyphony;
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            uint rgba = noteCol.rgba;
            byte r = (byte)((rgba >> 24) & 0xFF);
            byte g = (byte)((rgba >> 16) & 0xFF);
            byte b = (byte)((rgba >> 8) & 0xFF);
            byte a = (byte)(rgba & 0xFF);
            return new RaylibColor(r, g, b, a);
        }

        public void RenderKeyboard()
        {
            renderer.RenderKeyboard();
        }

        public void Dispose()
        {
            // Nothing to dispose for MainPlayer
        }
    }

    /// <summary>
    /// SecondPlayer pre-renders future notes to a sliding texture using the original Kiva buffer approach
    /// </summary>
    public class SecondPlayer : IDisposable
    {
        private RaylibRenderer renderer;
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private RenderTexture2D notesTexture;

        // Buffer management (similar to original Kiva)
        private double bufferStartTime = 0;
        private double bufferEndTime = 0;
        private double bufferDuration = 10.0; // 10 seconds of future notes (adjustable)

        // Future notes buffer system (from original Kiva)
        private int[] firstRenderNote = new int[256];
        private int[] firstUnhitNote = new int[256];
        private double lastRenderTime = 0;

        // Keyboard layout
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        // Statistics
        public long LastRenderedNoteCount { get; private set; } = 0;

        public double BufferStartTime => bufferStartTime;
        public double BufferDuration => bufferDuration;

        public MIDIFile File
        {
            get => file;
            set => file = value;
        }

        public PlayingState Time
        {
            get => time;
            set => time = value;
        }

        public SecondPlayer(RaylibRenderer renderer, Settings settings, RenderTexture2D notesTexture)
        {
            this.renderer = renderer;
            this.settings = settings;
            this.notesTexture = notesTexture;

            InitializeKeyboardLayout();
            ResetBuffer();
        }

        private void InitializeKeyboardLayout()
        {
            // Copy keyboard layout from MainPlayer
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            CalculateKeyboardLayout();
        }

        private bool IsBlackNote(int note)
        {
            int octaveNote = note % 12;
            return octaveNote == 1 || octaveNote == 3 || octaveNote == 6 || octaveNote == 8 || octaveNote == 10;
        }

        private void CalculateKeyboardLayout()
        {
            // Same layout calculation as MainPlayer
            int firstNote = 0;
            int lastNote = 128;

            switch (settings.General.KeyRange)
            {
                case KeyRangeTypes.Key88:
                    firstNote = 21;
                    lastNote = 109;
                    break;
                case KeyRangeTypes.Key128:
                    firstNote = 0;
                    lastNote = 128;
                    break;
                case KeyRangeTypes.Key256:
                    firstNote = 0;
                    lastNote = 256;
                    break;
                case KeyRangeTypes.KeyMIDI:
                    firstNote = settings.General.FirstKey;
                    lastNote = settings.General.LastKey + 1;
                    break;
                case KeyRangeTypes.KeyDynamic:
                    firstNote = settings.General.FirstKey;
                    lastNote = settings.General.LastKey + 1;
                    break;
                case KeyRangeTypes.Custom:
                    firstNote = settings.General.CustomFirstKey;
                    lastNote = settings.General.CustomLastKey + 1;
                    break;
            }

            double currentX = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (blackKeys[i])
                {
                    wdtharray[i] = 0.6;
                }
                else
                {
                    wdtharray[i] = 1.0;
                }

                x1array[i] = currentX;
                currentX += wdtharray[i];
            }

            fullLeft = x1array[firstNote];
            fullRight = currentX;
            fullWidth = fullRight - fullLeft;
        }

        private void ResetBuffer()
        {
            // Reset FirstRenderNote arrays (from original Kiva)
            for (int i = 0; i < firstRenderNote.Length; i++)
            {
                firstRenderNote[i] = 0;
                firstUnhitNote[i] = 0;
            }

            lastRenderTime = 0;
        }

        public void Update(double currentTime, bool forceReset)
        {
            if (file == null || time == null)
                return;

            if (forceReset || NeedsBufferUpdate(currentTime))
            {
                ResetBuffer();
                RenderFutureNotesToTexture(currentTime);
            }
        }

        private bool NeedsBufferUpdate(double currentTime)
        {
            // Check if we need to update the buffer
            return currentTime < bufferStartTime || currentTime > bufferEndTime - 2.0; // 2 second buffer
        }

        private void RenderFutureNotesToTexture(double currentTime)
        {
            if (!(file is MIDIMemoryFile memoryFile) || notesTexture.id == 0)
                return;

            // Calculate dynamic buffer duration based on note speed
            double timeScale = settings?.Volatile?.Size ?? 1.0;
            bufferDuration = Math.Max(5.0, Math.Min(20.0, 10.0 / timeScale)); // 5-20 seconds based on speed

            bufferStartTime = currentTime;
            bufferEndTime = currentTime + bufferDuration;

            // Begin rendering to texture
            Raylib.BeginTextureMode(notesTexture);
            Raylib.ClearBackground(new RaylibColor(0, 0, 0, 0)); // Transparent background

            try
            {
                RenderFutureNotes(memoryFile, currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering future notes: {ex.Message}");
            }
            finally
            {
                Raylib.EndTextureMode();
            }
        }

        private void RenderFutureNotes(MIDIMemoryFile memoryFile, double currentTime)
        {
            double timeScale = settings?.Volatile?.Size ?? 1.0;
            double renderCutoff = bufferEndTime; // Render all future notes in buffer

            var colors = memoryFile.MidiNoteColors;
            if (colors == null)
                return;

            long notesRendered = 0;

            // Set color events
            try
            {
                memoryFile.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (blackKeys[k]) continue;
                RenderFutureNotesForKey(memoryFile, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered);
            }

            // Render black key notes second (foreground layer)
            for (int k = 0; k < 256; k++)
            {
                if (!blackKeys[k]) continue;
                RenderFutureNotesForKey(memoryFile, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered);
            }

            LastRenderedNoteCount = notesRendered;
            lastRenderTime = currentTime;
        }

        private void RenderFutureNotesForKey(MIDIMemoryFile file, int k, double currentTime, double timeScale, double renderCutoff, NoteCol[] colors, ref long notesRendered)
        {
            if (file.Notes == null || k >= file.Notes.Length)
                return;

            var notes = file.Notes[k];
            if (notes == null || notes.Length == 0)
                return;

            // Use original piano layout positioning
            float left = (float)((x1array[k] - fullLeft) / fullWidth);
            float right = (float)((x1array[k] + wdtharray[k] - fullLeft) / fullWidth);

            // Use FirstRenderNote optimization (from original Kiva)
            int startIndex = firstRenderNote[k];

            // Handle seeking (time going backwards)
            if (currentTime < lastRenderTime)
            {
                startIndex = 0;
                for (int i = 0; i < notes.Length; i++)
                {
                    if (notes[i].end > currentTime - 1.0)
                    {
                        startIndex = i;
                        break;
                    }
                }
                firstRenderNote[k] = startIndex;
            }

            // Advance FirstRenderNote for notes that are completely done
            while (startIndex < notes.Length && notes[startIndex].end < currentTime - 1.0)
            {
                startIndex++;
            }
            firstRenderNote[k] = startIndex;

            // Render future notes using the original Kiva approach
            for (int i = startIndex; i < notes.Length; i++)
            {
                var note = notes[i];

                // If note starts too far in the future, we're done
                if (note.start > renderCutoff)
                    break;

                // Only render notes that will be visible in the future
                if (note.start >= currentTime && note.end >= currentTime)
                {
                    // Calculate note position in texture coordinates
                    float noteStart = (float)((note.start - currentTime) / timeScale * notesTexture.texture.height);
                    float noteEnd = (float)((note.end - currentTime) / timeScale * notesTexture.texture.height);

                    // Convert to screen coordinates for texture rendering
                    float noteLeft = left * notesTexture.texture.width;
                    float noteRight = right * notesTexture.texture.width;
                    float noteWidth = noteRight - noteLeft;
                    float noteHeight = noteEnd - noteStart;

                    if (noteHeight > 0 && noteWidth > 0)
                    {
                        var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                        // Draw note rectangle to texture with proper borders (matching original Kiva style)
                        RenderKivaNoteToTexture((int)noteLeft, (int)noteStart, (int)noteWidth, (int)noteHeight, noteColor);

                        notesRendered++;
                    }
                }
            }
        }

        private void RenderKivaNoteToTexture(int x, int y, int width, int height, RaylibColor noteColor)
        {
            // Ensure minimum dimensions for very short notes
            if (height < 2) height = 2;
            if (width < 2) width = 2;

            // Calculate border size (matching original Kiva style)
            int borderSize = Math.Max(1, width / 100); // Proportional border

            // Draw main note body
            Raylib.DrawRectangle(x, y, width, height, noteColor);

            // Draw darker borders for depth (matching original Kiva)
            RaylibColor borderColor = new RaylibColor(
                (byte)(noteColor.r * 0.7f),
                (byte)(noteColor.g * 0.7f),
                (byte)(noteColor.b * 0.7f),
                noteColor.a
            );

            // Top and bottom borders
            Raylib.DrawRectangle(x, y, width, borderSize, borderColor);
            Raylib.DrawRectangle(x, y + height - borderSize, width, borderSize, borderColor);

            // Left and right borders
            Raylib.DrawRectangle(x, y, borderSize, height, borderColor);
            Raylib.DrawRectangle(x + width - borderSize, y, borderSize, height, borderColor);

            // Add subtle gradient effect for very short notes (matching original behavior)
            if (height < 10)
            {
                RaylibColor darkerColor = new RaylibColor(
                    (byte)(noteColor.r * 0.8f),
                    (byte)(noteColor.g * 0.8f),
                    (byte)(noteColor.b * 0.8f),
                    noteColor.a
                );
                Raylib.DrawRectangle(x + borderSize, y + borderSize,
                                   width - 2 * borderSize, height - 2 * borderSize, darkerColor);
            }
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            uint rgba = noteCol.rgba;
            byte r = (byte)((rgba >> 24) & 0xFF);
            byte g = (byte)((rgba >> 16) & 0xFF);
            byte b = (byte)((rgba >> 8) & 0xFF);
            byte a = (byte)(rgba & 0xFF);
            return new RaylibColor(r, g, b, a);
        }

        public void Dispose()
        {
            // Nothing to dispose for SecondPlayer (texture is managed by DualPlayer)
        }
    }
}
